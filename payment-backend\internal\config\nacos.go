package config

import (
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/nacos-group/nacos-sdk-go/v2/clients"
	"github.com/nacos-group/nacos-sdk-go/v2/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/v2/common/constant"
	"github.com/nacos-group/nacos-sdk-go/v2/vo"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// NacosClient Nacos配置客户端
type NacosClient struct {
	client config_client.IConfigClient
	config NacosConfig
}

// ConfigChangeCallback 配置变更回调函数类型
type ConfigChangeCallback func(namespace, group, dataId, data string)

// NewNacosClient 创建Nacos配置客户端
func NewNacosClient(config NacosConfig) (*NacosClient, error) {
	if !config.Enabled {
		return nil, nil
	}

	// 解析服务器配置
	serverConfigs := make([]constant.ServerConfig, 0, len(config.Endpoints))
	for _, endpoint := range config.Endpoints {
		parts := strings.Split(endpoint, ":")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid nacos endpoint format: %s, expected host:port", endpoint)
		}

		port, err := strconv.ParseUint(parts[1], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid port in nacos endpoint %s: %w", endpoint, err)
		}

		serverConfigs = append(serverConfigs, constant.ServerConfig{
			IpAddr:      parts[0],
			Port:        port,
			ContextPath: "/nacos",
		})
	}

	// 客户端配置
	clientConfig := &constant.ClientConfig{
		NamespaceId:         config.Namespace,
		Username:            config.Username,
		Password:            config.Password,
		TimeoutMs:           uint64(config.Config.Timeout * 1000), // 转换为毫秒
		LogLevel:            "error",
		AppendToStdout:      false,
		NotLoadCacheAtStart: true,
	}

	// 创建配置客户端
	client, err := clients.NewConfigClient(vo.NacosClientParam{
		ClientConfig:  clientConfig,
		ServerConfigs: serverConfigs,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos config client: %w", err)
	}

	return &NacosClient{
		client: client,
		config: config,
	}, nil
}

// GetConfig 获取配置内容
func (nc *NacosClient) GetConfig() (string, error) {
	if nc == nil || nc.client == nil {
		return "", nil
	}

	content, err := nc.client.GetConfig(vo.ConfigParam{
		DataId: nc.config.Config.DataID,
		Group:  nc.config.Config.Group,
	})
	if err != nil {
		return "", fmt.Errorf("failed to get config from nacos: %w", err)
	}

	return content, nil
}

// ListenConfig 监听配置变更
func (nc *NacosClient) ListenConfig(callback ConfigChangeCallback) error {
	if nc == nil || nc.client == nil {
		return nil
	}

	err := nc.client.ListenConfig(vo.ConfigParam{
		DataId: nc.config.Config.DataID,
		Group:  nc.config.Config.Group,
		OnChange: func(namespace, group, dataId, data string) {
			log.Printf("Nacos config changed: namespace=%s, group=%s, dataId=%s", namespace, group, dataId)
			if callback != nil {
				callback(namespace, group, dataId, data)
			}
		},
	})
	if err != nil {
		return fmt.Errorf("failed to listen config changes: %w", err)
	}

	return nil
}

// LoadConfigFromNacos 从Nacos加载配置到viper
func LoadConfigFromNacos(v *viper.Viper, nacosConfig NacosConfig) error {
	if !nacosConfig.Enabled || !nacosConfig.Config.Enabled {
		return nil
	}

	// 创建Nacos客户端
	client, err := NewNacosClient(nacosConfig)
	if err != nil {
		return fmt.Errorf("failed to create nacos client: %w", err)
	}

	if client == nil {
		return nil
	}

	// 获取配置内容
	content, err := client.GetConfig()
	if err != nil {
		return fmt.Errorf("failed to get config from nacos: %w", err)
	}

	if content == "" {
		log.Printf("No config found in nacos for dataId: %s, group: %s", nacosConfig.Config.DataID, nacosConfig.Config.Group)
		return nil
	}

	// 解析YAML配置
	var configMap map[string]interface{}
	if err := yaml.Unmarshal([]byte(content), &configMap); err != nil {
		return fmt.Errorf("failed to parse nacos config as yaml: %w", err)
	}

	// 将配置合并到viper中
	for key, value := range configMap {
		v.Set(key, value)
	}

	log.Printf("Successfully loaded config from nacos: dataId=%s, group=%s", nacosConfig.Config.DataID, nacosConfig.Config.Group)
	return nil
}

// StartConfigWatch 启动配置监听
func StartConfigWatch(nacosConfig NacosConfig, callback ConfigChangeCallback) (*NacosClient, error) {
	if !nacosConfig.Enabled || !nacosConfig.Config.Enabled {
		return nil, nil
	}

	client, err := NewNacosClient(nacosConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos client for watching: %w", err)
	}

	if client == nil {
		return nil, nil
	}

	// 启动配置监听
	if err := client.ListenConfig(callback); err != nil {
		return nil, fmt.Errorf("failed to start config watching: %w", err)
	}

	log.Printf("Started nacos config watching: dataId=%s, group=%s", nacosConfig.Config.DataID, nacosConfig.Config.Group)
	return client, nil
}

// ParseNacosEndpoints 解析Nacos endpoints环境变量
func ParseNacosEndpoints(endpoints string) []string {
	if endpoints == "" {
		return []string{"127.0.0.1:8848"}
	}

	// 支持逗号分隔的多个endpoint
	parts := strings.Split(endpoints, ",")
	result := make([]string, 0, len(parts))
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	if len(result) == 0 {
		return []string{"127.0.0.1:8848"}
	}

	return result
}
