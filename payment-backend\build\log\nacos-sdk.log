2025-07-14T09:39:59.709+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:39:59.713+0800	INFO	nacos_client/nacos_client.go:65	logDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\log> cacheDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache>
2025-07-14T09:39:59.714+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:39:59.717+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55624
2025-07-14T09:39:59.717+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.773+0800	INFO	util/common.go:96	Local IP:************
2025-07-14T09:39:59.773+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=2
2025-07-14T09:39:59.773+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.774+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=1
2025-07-14T09:39:59.774+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.774+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] 49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=0
2025-07-14T09:39:59.774+0800	INFO	rpc/rpc_client.go:324	49e0af64-5c0a-45fc-814c-720635fe7bf4 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-14T09:39:59.775+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=2
2025-07-14T09:39:59.776+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=1
2025-07-14T09:39:59.776+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-14T09:39:59.776+0800	WARN	rpc/rpc_client.go:226	[RpcClient.Start] config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it.", start up retry times left=0
2025-07-14T09:39:59.777+0800	INFO	rpc/rpc_client.go:324	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-14T09:39:59.777+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:39:59.778+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.877+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.877+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:39:59.878+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:39:59.977+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.078+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.078+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:00.078+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:00.078+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.078+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.179+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.279+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.379+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 4 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.379+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 4 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.380+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.480+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.581+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.681+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:00.681+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:00.681+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.781+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 5 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.781+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 5 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:00.781+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.882+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:00.982+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.082+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.183+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.281+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 6 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.281+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 6 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.283+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:01.283+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:01.283+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.384+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.484+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.584+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.685+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.785+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.881+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 7 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.881+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 7 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:01.886+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:01.886+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:01.886+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:01.987+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.087+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.188+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.289+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.389+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.489+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:02.489+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:02.489+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.582+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 8 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:02.582+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 8 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:02.590+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.690+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.790+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.891+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:02.991+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.091+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:03.091+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:03.091+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.192+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.292+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.383+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 9 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:03.383+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 9 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:03.392+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.492+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.593+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.693+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:03.694+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:03.694+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.794+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.894+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:03.995+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.095+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.195+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.285+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 10 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:04.285+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 10 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:04.296+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:04.296+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:04.296+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.396+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.497+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.597+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.698+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.712+0800	ERROR	security/security_proxy.go:90	login has error Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:40:04.717+0800	ERROR	security/security_proxy.go:90	login has error Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-14T09:40:04.799+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:04.899+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:04.899+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:04.899+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.000+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.100+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.201+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.286+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 11 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:05.286+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 11 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:05.301+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.402+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).getConfig
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:126
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:164
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.502+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=com.aibook.payment.grpc.OrderService, group=mapping, namespaceId=payment-service-dev
2025-07-14T09:40:05.502+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\com.aibook.payment.grpc.OrderService@@mapping@@payment-service-dev: The system cannot find the path specified. 
2025-07-14T09:40:05.502+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.602+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:05.702+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigPublishRequest, body={"requestId":"","group":"mapping","dataId":"com.aibook.payment.grpc.OrderService","tenant":"payment-service-dev","module":"config","content":"payment-service","casMd5":"","additionMap":{"appName":"","betaIps":"","encryptedDataKey":"","tag":"","type":""}}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).PublishConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:252
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).storeMetadata
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:114
dubbo.apache.org/dubbo-go/v3/metadata/report/nacos.(*nacosMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report/nacos/report.go:179
dubbo.apache.org/dubbo-go/v3/metadata.(*DelegateMetadataReport).RegisterServiceAppMapping
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/report_instance.go:118
dubbo.apache.org/dubbo-go/v3/metadata/mapping/metadata.(*ServiceNameMapping).Map
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/metadata/mapping/metadata/service_name_mapping.go:78
dubbo.apache.org/dubbo-go/v3/registry/servicediscovery.(*serviceDiscoveryRegistry).Register
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/servicediscovery/service_discovery_registry.go:203
dubbo.apache.org/dubbo-go/v3/registry/protocol.(*registryProtocol).Export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/registry/protocol/protocol.go:209
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).export
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:230
dubbo.apache.org/dubbo-go/v3/server.(*ServiceOptions).ExportWithInfo
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/action.go:132
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices.func1
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:136
internal/sync.(*HashTrieMap[...]).iter
	C:/file/program/go/src/internal/sync/hashtriemap.go:512
internal/sync.(*HashTrieMap[...]).Range
	C:/file/program/go/src/internal/sync/hashtriemap.go:495
sync.(*Map).Range
	C:/file/program/go/src/sync/hashtriemap.go:115
dubbo.apache.org/dubbo-go/v3/server.(*Server).exportServices
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:118
dubbo.apache.org/dubbo-go/v3/server.(*Server).Serve
	C:/Users/<USER>/go/pkg/mod/dubbo.apache.org/dubbo-go/v3@v3.3.0/server/server.go:162
payment-backend/internal/dubbo/server.(*DubboServer).Start
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/dubbo/server/dubbo_server.go:145
payment-backend/internal/app.init.func9.1.1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:132
runtime.goexit
	C:/file/program/go/src/runtime/asm_amd64.s:1700
2025-07-14T09:40:06.386+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 12 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:06.386+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 12 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:07.586+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 13 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:07.586+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 13 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:08.887+0800	WARN	rpc/rpc_client.go:355	49e0af64-5c0a-45fc-814c-720635fe7bf4 fail to connect server, after trying 14 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-14T09:40:08.887+0800	WARN	rpc/rpc_client.go:355	config-0-7edfd3a6-1c73-49c3-bfba-b744c7996a78 fail to connect server, after trying 14 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connectex: No connection could be made because the target machine actively refused it."
2025-07-15T14:59:50.806+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-15T14:59:50.861+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T14:59:50.961+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T14:59:51.061+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T14:59:51.162+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-15T14:59:51.162+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the path specified. 
2025-07-15T15:51:54.188+0800	ERROR	cache/disk_cache.go:86	failed to delete config file,cache:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1 ,value: ,err:remove C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1: The system cannot find the file specified.
2025-07-15T15:53:01.499+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:53:10.729+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"dev1","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:345
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:176
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T15:53:10.836+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"dev1","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:345
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:176
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T15:53:10.944+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"dev1","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:345
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:176
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T15:53:11.052+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=dev1
2025-07-15T15:53:11.052+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@dev1, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1: The system cannot find the file specified. 
2025-07-15T15:53:27.633+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:53:53.677+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:54:19.711+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:54:45.744+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:55:11.777+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:55:37.799+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:56:03.843+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:56:29.877+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:56:55.932+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:57:21.971+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:57:47.985+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:58:14.021+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:58:40.051+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:59:06.100+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:59:32.121+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T15:59:58.166+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:00:24.198+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:00:50.239+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:01:16.275+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:01:42.309+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:02:08.406+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:02:34.434+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:03:00.474+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:03:26.496+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:03:52.538+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:04:18.582+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:04:44.621+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:05:10.659+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:05:36.695+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:06:02.740+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:06:28.763+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:06:54.791+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:07:20.815+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:07:46.853+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:08:13.027+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:08:39.065+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:09:05.095+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:09:31.116+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:09:57.168+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:10:23.203+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:10:49.230+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:11:15.274+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:11:41.329+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:12:07.373+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:12:33.418+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:12:59.467+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:13:25.522+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:13:51.553+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:14:17.582+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:14:43.604+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:15:09.647+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:15:35.678+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:16:01.719+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:16:27.752+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:16:53.796+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:17:19.824+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:17:45.854+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:18:11.908+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:18:37.951+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:19:03.994+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:19:30.024+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:19:56.068+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:20:22.104+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:20:48.135+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:21:14.162+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:21:40.197+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:22:06.241+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:22:32.277+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:22:58.309+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:23:24.535+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:23:50.576+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:24:16.599+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:24:42.648+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:25:08.693+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:25:34.724+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:26:00.774+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:26:26.820+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:26:52.856+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:27:18.904+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:27:44.946+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:28:10.979+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:28:37.025+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:29:03.061+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:29:29.096+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:29:55.142+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:30:21.184+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:30:47.218+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:31:13.256+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:31:39.294+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:32:05.327+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:32:31.360+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:32:57.403+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:33:23.447+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:33:49.501+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:34:15.535+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:34:41.584+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:35:07.629+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:35:33.666+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:35:59.702+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:36:25.749+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:36:51.801+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:37:17.863+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:37:43.908+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:38:10.040+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:38:36.084+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:39:02.121+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:39:28.157+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:39:54.222+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:40:20.264+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:40:46.300+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:41:12.347+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:41:38.386+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:42:04.431+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:42:30.500+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:42:56.547+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:43:22.588+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:43:48.644+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:44:14.705+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:44:40.758+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:45:06.808+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:45:32.838+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:45:58.866+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:46:24.890+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:46:50.931+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:47:16.977+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:47:43.019+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:48:09.058+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:48:35.113+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:49:01.141+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:49:27.173+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:49:53.222+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:50:19.260+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:50:45.300+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:51:11.353+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:51:37.393+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:52:03.421+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:52:29.449+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:52:55.489+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:53:21.532+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:53:47.579+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:54:13.611+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:54:39.647+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:55:05.673+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:55:31.709+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:55:57.748+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:56:23.784+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:56:49.800+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:57:15.842+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:57:41.872+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:58:07.917+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:58:33.948+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:58:59.990+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:59:26.049+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T16:59:52.093+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:00:18.151+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:00:44.201+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:01:10.233+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:01:36.247+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:02:02.272+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:02:28.323+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:02:54.359+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:03:20.395+0800	ERROR	security/security_proxy.go:90	login has error Post "http://*************:8848/nacos/v1/auth/users/login": dial tcp *************:8848: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
2025-07-15T17:21:48.730+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-15T17:21:48.757+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:21:48.858+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:21:48.958+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:21:49.058+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-15T17:21:49.058+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-15T17:22:31.636+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-15T17:22:31.663+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:22:31.764+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:22:31.865+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-15T17:22:31.966+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-15T17:22:31.966+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T08:49:46.214+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T08:49:46.255+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T08:49:46.356+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T08:49:46.457+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T08:49:46.557+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T08:49:46.558+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T09:21:52.912+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T09:21:52.971+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T09:21:53.072+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T09:21:53.173+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T09:21:53.274+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T09:21:53.274+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T10:18:58.588+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T10:18:58.652+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:18:58.753+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:18:58.854+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:18:58.954+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T10:18:58.954+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T10:19:41.463+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T10:19:41.551+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:19:41.653+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:19:41.753+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:19:41.854+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T10:19:41.854+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T10:20:26.596+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T10:20:26.678+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:20:26.779+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:20:26.879+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:344
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:20:26.980+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T10:20:26.980+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
2025-07-16T10:24:53.418+0800	ERROR	nacos_server/nacos_server.go:89	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connectex: No connection could be made because the target machine actively refused it.
2025-07-16T10:24:53.495+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:348
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:24:53.595+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:348
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:24:53.696+0800	ERROR	rpc/rpc_client.go:524	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"payment-backend.yaml","tenant":"payment-service","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/common/remote/rpc/rpc_client.go:489
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:205
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	C:/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.2/clients/config_client/config_client.go:149
payment-backend/internal/config.(*NacosClient).GetConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:84
payment-backend/internal/config.LoadConfigFromNacos
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/nacos.go:135
payment-backend/internal/config.loadNacosConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:348
payment-backend/internal/config.LoadConfig
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/config/config.go:175
payment-backend/internal/app.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:27
reflect.Value.call
	C:/file/program/go/src/reflect/value.go:581
reflect.Value.Call
	C:/file/program/go/src/reflect/value.go:365
go.uber.org/dig.defaultInvoker
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/container.go:257
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:198
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*constructorNode).Call
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/constructor.go:160
go.uber.org/dig.paramSingle.Build
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:288
go.uber.org/dig.paramList.BuildList
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/param.go:151
go.uber.org/dig.(*Scope).Invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/dig@v1.19.0/invoke.go:123
go.uber.org/fx.runInvoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/invoke.go:109
go.uber.org/fx.(*module).invoke
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:335
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:321
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.(*module).invokeAll
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/module.go:315
go.uber.org/fx.New
	C:/Users/<USER>/go/pkg/mod/go.uber.org/fx@v1.24.0/app.go:507
payment-backend/internal/app.NewApp
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/internal/app/app.go:160
payment-backend/cmd.init.func1
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/serve.go:36
github.com/spf13/cobra.(*Command).execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1019
github.com/spf13/cobra.(*Command).ExecuteC
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1148
github.com/spf13/cobra.(*Command).Execute
	C:/Users/<USER>/go/pkg/mod/github.com/spf13/cobra@v1.9.1/command.go:1071
payment-backend/cmd.Execute
	C:/file/z/iscas/gitlab/aibook/platform/payment-service/payment-backend/cmd/root.go:41
2025-07-16T10:24:53.796+0800	ERROR	config_client/config_client.go:208	get config from server error:client not connected, current status:STARTING, dataId=payment-backend.yaml, group=DEFAULT_GROUP, namespaceId=payment-service
2025-07-16T10:24:53.796+0800	ERROR	cache/disk_cache.go:101	get config from cache, cacheKey:payment-backend.yaml@@DEFAULT_GROUP@@payment-service, cacheDir:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config, error:open C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\build\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@payment-service: The system cannot find the file specified. 
