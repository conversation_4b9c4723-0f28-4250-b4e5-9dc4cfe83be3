package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Database  DatabaseConfig  `mapstructure:"database"`
	Payment   PaymentConfig   `mapstructure:"payment"`
	Log       LogConfig       `mapstructure:"log"`
	Snowflake SnowflakeConfig `mapstructure:"snowflake"`
	Admin     AdminConfig     `mapstructure:"admin"`
	Dubbo     DubboConfig     `mapstructure:"dubbo"`
	Nacos     NacosConfig     `mapstructure:"nacos"`
}

// ServerConfig HTTP服务器配置
type ServerConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	Mode         string `mapstructure:"mode"` // debug, release, test
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver   string `mapstructure:"driver"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	SSLMode  string `mapstructure:"ssl_mode"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	Providers map[string]ProviderConfig `mapstructure:"providers"`
}

// ProviderConfig 支付提供商配置
type ProviderConfig struct {
	Enabled    bool              `mapstructure:"enabled"`
	APIKey     string            `mapstructure:"api_key"`
	SecretKey  string            `mapstructure:"secret_key"`
	SuccessURL string            `mapstructure:"success_url"`
	CancelURL  string            `mapstructure:"cancel_url"`
	Webhook    WebhookConfig     `mapstructure:"webhook"`
	Settings   map[string]string `mapstructure:"settings"`
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	URL    string `mapstructure:"url"`
	Secret string `mapstructure:"secret"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"` // json, console
	Output     string `mapstructure:"output"` // stdout, file
	Filename   string `mapstructure:"filename"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
}

// SnowflakeConfig 雪花算法配置
type SnowflakeConfig struct {
	NodeID int64 `mapstructure:"node_id"`
}

// AdminConfig 管理员配置
type AdminConfig struct {
	AllowedRoles []string         `mapstructure:"allowed_roles"`
	Pagination   PaginationConfig `mapstructure:"pagination"`
}

// PaginationConfig 分页配置
type PaginationConfig struct {
	DefaultLimit int `mapstructure:"default_limit"`
	MaxLimit     int `mapstructure:"max_limit"`
}

// DubboConfig Dubbo配置
type DubboConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	IP      string `mapstructure:"ip"`
}

// NacosConfig Nacos配置（统一配置中心和注册中心）
type NacosConfig struct {
	Enabled   bool     `mapstructure:"enabled"`   // 是否启用Nacos
	Endpoints []string `mapstructure:"endpoints"` // Nacos服务器地址列表
	Namespace string   `mapstructure:"namespace"` // 命名空间
	Username  string   `mapstructure:"username"`  // 用户名
	Password  string   `mapstructure:"password"`  // 密码

	// 配置中心相关
	Config NacosConfigCenter `mapstructure:"config"` // 配置中心设置

	// 注册中心相关
	Registry NacosRegistry `mapstructure:"registry"` // 注册中心设置
}

// NacosConfigCenter Nacos配置中心配置
type NacosConfigCenter struct {
	Enabled bool   `mapstructure:"enabled"` // 是否启用配置中心
	DataID  string `mapstructure:"data_id"` // 配置文件ID
	Group   string `mapstructure:"group"`   // 配置组
	Timeout int    `mapstructure:"timeout"` // 连接超时时间(秒)
}

// NacosRegistry Nacos注册中心配置
type NacosRegistry struct {
	Enabled bool `mapstructure:"enabled"` // 是否启用注册中心
}

// getEnvironment 获取当前运行环境
func getEnvironment() string {
	// 优先使用 PAYMENT_ENV 环境变量
	if env := os.Getenv("PAYMENT_ENV"); env != "" {
		return env
	}

	// 其次使用 GO_ENV 环境变量
	if env := os.Getenv("GO_ENV"); env != "" {
		return env
	}

	// 默认为生产环境
	return "prod"
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	v := viper.New()

	// 设置环境变量前缀
	v.SetEnvPrefix("PAYMENT")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 设置默认值
	setDefaults(v)

	// 如果指定了配置文件路径，只加载指定的文件
	if configPath != "" {
		v.SetConfigFile(configPath)
		if err := v.ReadInConfig(); err != nil {
			if os.IsNotExist(err) {
				// 指定的配置文件不存在，使用默认值
			} else {
				// 其他错误（如格式错误等）
				return nil, fmt.Errorf("failed to read config file: %w", err)
			}
		}
	} else {
		// 自动加载配置文件：先加载基础配置，再加载环境特定配置
		if err := loadConfigFiles(v); err != nil {
			return nil, fmt.Errorf("failed to load config files: %w", err)
		}
	}

	// 从Nacos加载配置（优先级：配置文件 < Nacos < 环境变量）
	if err := loadNacosConfig(v); err != nil {
		return nil, fmt.Errorf("failed to load nacos config: %w", err)
	}

	fmt.Printf("========= LoadConfig, configPath:%v \n", configPath)

	// 解析配置
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	fmt.Printf("========= LoadConfig, config.Snowflake:%v \n", config.Snowflake)

	return &config, nil
}

// ConfigManager 配置管理器
type ConfigManager struct {
	config      *Config
	nacosClient *NacosClient
	callbacks   []func(*Config)
}

// NewConfigManager 创建配置管理器
func NewConfigManager(configPath string) (*ConfigManager, error) {
	// 加载初始配置
	config, err := LoadConfig(configPath)
	if err != nil {
		return nil, err
	}

	cm := &ConfigManager{
		config:    config,
		callbacks: make([]func(*Config), 0),
	}

	// 如果启用了Nacos，启动配置监听
	if config.Nacos.Enabled {
		nacosClient, err := StartConfigWatch(config.Nacos, cm.onConfigChange)
		if err != nil {
			return nil, fmt.Errorf("failed to start nacos config watch: %w", err)
		}
		cm.nacosClient = nacosClient
	}

	return cm, nil
}

// GetConfig 获取当前配置
func (cm *ConfigManager) GetConfig() *Config {
	return cm.config
}

// AddCallback 添加配置变更回调
func (cm *ConfigManager) AddCallback(callback func(*Config)) {
	cm.callbacks = append(cm.callbacks, callback)
}

// onConfigChange 处理配置变更
func (cm *ConfigManager) onConfigChange(namespace, group, dataId, data string) {
	fmt.Printf("Config changed from nacos: namespace=%s, group=%s, dataId=%s\n", namespace, group, dataId)

	// 重新加载配置
	newConfig, err := LoadConfig("")
	if err != nil {
		fmt.Printf("Error reloading config after nacos change: %v\n", err)
		return
	}

	// 更新配置
	cm.config = newConfig

	// 调用所有回调函数
	for _, callback := range cm.callbacks {
		callback(newConfig)
	}
}

// loadConfigFiles 依次加载配置文件
func loadConfigFiles(v *viper.Viper) error {
	configPaths := []string{"./configs", ".", "$HOME/.payment-backend"}

	// 1. 先加载基础配置文件 config.yaml
	baseConfigLoaded := false
	for _, path := range configPaths {
		configFile := "config.yaml"
		if path == "." {
			configFile = "config.yaml"
		} else if strings.HasPrefix(path, "$HOME") {
			// 展开 $HOME 路径
			home, err := os.UserHomeDir()
			if err == nil {
				actualPath := strings.Replace(path, "$HOME", home, 1)
				configFile = fmt.Sprintf("%s/config.yaml", actualPath)
			}
		} else {
			configFile = fmt.Sprintf("%s/config.yaml", path)
		}

		// 检查基础配置文件是否存在
		if _, err := os.Stat(configFile); err == nil {
			// 文件存在，加载配置
			v.SetConfigFile(configFile)
			if err := v.ReadInConfig(); err != nil {
				return fmt.Errorf("failed to read base config file %s: %w", configFile, err)
			}
			baseConfigLoaded = true
			break
		}
	}

	// 2. 再加载环境特定配置文件 config.{env}.yaml
	env := getEnvironment()
	envConfigName := fmt.Sprintf("config.%s.yaml", env)

	for _, path := range configPaths {
		envConfigFile := envConfigName
		if path == "." {
			envConfigFile = envConfigName
		} else if strings.HasPrefix(path, "$HOME") {
			// 展开 $HOME 路径
			home, err := os.UserHomeDir()
			if err == nil {
				actualPath := strings.Replace(path, "$HOME", home, 1)
				envConfigFile = fmt.Sprintf("%s/%s", actualPath, envConfigName)
			}
		} else {
			envConfigFile = fmt.Sprintf("%s/%s", path, envConfigName)
		}

		// 检查环境配置文件是否存在
		if _, err := os.Stat(envConfigFile); err == nil {
			// 文件存在，合并配置
			v.SetConfigFile(envConfigFile)
			if err := v.MergeInConfig(); err != nil {
				return fmt.Errorf("failed to merge env config file %s: %w", envConfigFile, err)
			}
			break
		}
	}

	// 如果基础配置文件也没找到，这是正常的（使用默认值）
	if !baseConfigLoaded {
		// 可以记录日志，但不返回错误
	}

	return nil
}

// loadNacosConfig 从Nacos加载配置
func loadNacosConfig(v *viper.Viper) error {
	// 首先解析Nacos配置（从已加载的配置文件和环境变量中获取）
	var nacosConfig NacosConfig
	if err := v.UnmarshalKey("nacos", &nacosConfig); err != nil {
		return fmt.Errorf("failed to unmarshal nacos config: %w", err)
	}

	// 处理环境变量中的endpoints（支持逗号分隔的字符串）
	// 环境变量PAYMENT_NACOS_ENDPOINTS会被viper映射为nacos.endpoints
	if endpointsStr := v.GetString("nacos.endpoints"); endpointsStr != "" {
		// 如果从环境变量获取到的是字符串，需要解析为数组
		if len(nacosConfig.Endpoints) == 1 && nacosConfig.Endpoints[0] == endpointsStr {
			nacosConfig.Endpoints = ParseNacosEndpoints(endpointsStr)
		}
	}

	// 如果Nacos或配置中心未启用，直接返回
	if !nacosConfig.Enabled || !nacosConfig.Config.Enabled {
		return nil
	}

	// 从Nacos加载配置
	if err := LoadConfigFromNacos(v, nacosConfig); err != nil {
		// 记录错误但不中断启动，允许服务在Nacos不可用时仍能启动
		fmt.Printf("Warning: failed to load config from nacos: %v\n", err)
		return nil
	}

	return nil
}

// setDefaults 设置默认配置值
func setDefaults(v *viper.Viper) {
	// 服务器默认配置
	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.port", 8080)
	v.SetDefault("server.read_timeout", 30)
	v.SetDefault("server.write_timeout", 30)
	v.SetDefault("server.mode", "release")

	// 数据库默认配置
	v.SetDefault("database.driver", "mysql")
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 3306)
	v.SetDefault("database.username", "payment_user")
	v.SetDefault("database.password", "payment_password")
	v.SetDefault("database.database", "payment_db")
	v.SetDefault("database.ssl_mode", "disable")

	// 支付默认配置
	v.SetDefault("payment.providers.stripe.enabled", false)
	v.SetDefault("payment.providers.stripe.api_key", "")
	v.SetDefault("payment.providers.stripe.secret_key", "")
	v.SetDefault("payment.providers.stripe.success_url", "")
	v.SetDefault("payment.providers.stripe.cancel_url", "")
	v.SetDefault("payment.providers.stripe.webhook.url", "")
	v.SetDefault("payment.providers.stripe.webhook.secret", "")
	v.SetDefault("payment.providers.stripe.settings.environment", "test")

	v.SetDefault("payment.providers.paypal.enabled", false)
	v.SetDefault("payment.providers.paypal.api_key", "")
	v.SetDefault("payment.providers.paypal.secret_key", "")
	v.SetDefault("payment.providers.paypal.success_url", "")
	v.SetDefault("payment.providers.paypal.cancel_url", "")
	v.SetDefault("payment.providers.paypal.webhook.url", "")
	v.SetDefault("payment.providers.paypal.webhook.secret", "")
	v.SetDefault("payment.providers.paypal.settings.environment", "sandbox")

	// 日志默认配置
	v.SetDefault("log.level", "info")
	v.SetDefault("log.format", "json")
	v.SetDefault("log.output", "stdout")
	v.SetDefault("log.filename", "logs/payment-backend.log")
	v.SetDefault("log.max_size", 50)
	v.SetDefault("log.max_backups", 14)
	v.SetDefault("log.max_age", 28)

	// 雪花算法默认配置
	v.SetDefault("snowflake.node_id", 1)

	// 管理员默认配置
	v.SetDefault("admin.allowed_roles", []string{"admin", "super_admin"})
	v.SetDefault("admin.pagination.default_limit", 50)
	v.SetDefault("admin.pagination.max_limit", 500)

	// Dubbo默认配置
	v.SetDefault("dubbo.enabled", false)
	v.SetDefault("dubbo.port", 20000)
	v.SetDefault("dubbo.ip", "0.0.0.0")

	// Nacos默认配置（统一配置中心和注册中心）
	v.SetDefault("nacos.enabled", false)
	v.SetDefault("nacos.endpoints", []string{"127.0.0.1:8848"})
	v.SetDefault("nacos.namespace", "payment-service")
	v.SetDefault("nacos.username", "nacos")
	v.SetDefault("nacos.password", "nacos")

	// Nacos配置中心默认配置
	v.SetDefault("nacos.config.enabled", false)
	v.SetDefault("nacos.config.data_id", "payment-backend.yaml")
	v.SetDefault("nacos.config.group", "DEFAULT_GROUP")
	v.SetDefault("nacos.config.timeout", 30)

	// Nacos注册中心默认配置
	v.SetDefault("nacos.registry.enabled", false)
}

// GetAddress 获取服务器地址
func (c *ServerConfig) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	switch c.Driver {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.Database)
	case "postgres":
		return fmt.Sprintf("%s://%s:%s@%s:%d/%s?sslmode=%s",
			c.Driver, c.Username, c.Password, c.Host, c.Port, c.Database, c.SSLMode)
	default:
		// 默认使用MySQL格式
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			c.Username, c.Password, c.Host, c.Port, c.Database)
	}
}
